| 系统 | 模块 | 一级功能 | 二级功能点 |
|------|------|----------|------------|
| 区块链服务平台“物联网支持”系统 | 数据采集 | 传感器集成与管理 | 支持温度、湿度、光照强度、振动、压力、位置等多种类型传感器的接入与配置。 |
|      |      |          | 实时监测传感器的工作状态，包括在线/离线、故障报警等。 |
|      |      | 采集数据推送 | 以集中API对接方式将物联数据实时推送到数据平台 |
|      | 数据传输 | 物联网通信协议 | 采用标准物联网通信协议，确保数据传输的实时性和可靠性。 |
|      |      | 数据加密与传输安全 | 对传输的数据进行加密处理，确保数据传输过程中的安全性。 |
|      |      | 区块链数据传输验证 | 利用区块链技术记录数据传输的哈希值或摘要，以验证数据在传输过程中未被篡改。 |
|      |      | 区块链辅助的数据传输审计 | 通过区块链记录数据传输的日志或事件，提供可追溯的审计功能，增强数据传输的透明度和可信度。 |
|      | 数据处理 | 数据清洗与校验 | 对采集到的数据进行清洗和校验，剔除异常数据，确保数据质量。 |
|      |      | 数据存储 | 采用分布式数据库存储采集到的数据，支持高并发访问和海量数据存储。 |
|      |      | 区块链辅助的数据溯源 | 为处理后的数据添加区块链时间戳或唯一标识，实现数据的可追溯性，便于问题追踪和责任划分。 |
|      | 数据应用 | 业务规则管理 | 支持定义工作业务规则、异常排除规则等 |
|      |      | 业务数据提取 | 按业务规则进行数据定制处理，提取具体工作的开始时间、结束时间、时长的数据。 |
|      |      | 业务数据使用 | 与上层应用系统进行有机结合提供采集并处理后对业务数据内容的API对接推送。 |
|      |      | 电子围栏管理 | 支持用户自定义多边形电子围栏设置，人员及车辆定位 |
|      | 系统基础平台 | 基础框架 | 平台基础功能，包括系统架构和平台基础数据。 |
|      | 系统管理 | 用户权限管理 | 支持多级用户权限设置，确保系统数据的安全性和访问控制。 |
|      |      | 系统配置与运维 | 提供系统参数配置界面，支持远程运维操作，降低运维成本。 |
|      | 接口管理 | API接口定义 | 设计标准化的接口，明确每个接口的请求方式、参数、返回值等，供内部系统和外部应用调用。 |
|      |      | 接口权限控制 | 对不同的接口设置访问权限，确保数据的安全性和合规性。 |