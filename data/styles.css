﻿.ax_default {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
.box_1 {
}
.box_2 {
}
.ellipse {
}
.box_3 {
}
.paragraph {
  text-align:left;
}
.line {
}
.radio_button {
  text-align:left;
}
.heading_1 {
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_2 {
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.horizontal_line {
}
.shape {
}
.connector {
}
.image {
}
.line1 {
}
.heading_11 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_21 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.heading_4 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
.heading_5 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
.heading_6 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.paragraph1 {
  text-align:left;
}
.table_cell {
}
.sticky_1 {
  text-align:left;
}
.sticky_2 {
  text-align:left;
}
.sticky_3 {
  text-align:left;
}
.sticky_4 {
  text-align:left;
}
.marker {
  color:#FFFFFF;
}
._正文-常规-中 {
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
.flow_shape {
}
.image1 {
  color:#000000;
}
.placeholder {
}
.iconfont {
  font-family:'iconfont', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#666666;
}
.image2 {
}
.icon {
}
._文本链接 {
  color:#0000FF;
}
.el-button {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:center;
}
.el-button-primary {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:center;
}
.el-shadow {
}
.refs-design-fluent {
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
.refs-chart-model {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
}
textarea, select, input, button { outline: none; }
