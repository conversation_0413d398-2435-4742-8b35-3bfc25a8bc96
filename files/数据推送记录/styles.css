﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:2658px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u729_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:66px;
}
#u729 {
  border-width:0px;
  position:absolute;
  left:1982px;
  top:50px;
  width:440px;
  height:66px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u729 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u730 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u731 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u732_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u732 {
  border-width:0px;
  position:absolute;
  left:2394px;
  top:168px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u732 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u733_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u733 {
  border-width:0px;
  position:absolute;
  left:2633px;
  top:178px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u733 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u734_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u734 {
  border-width:0px;
  position:absolute;
  left:2394px;
  top:200px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u735 {
  border-width:0px;
  position:absolute;
  left:2323px;
  top:168px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u735 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u735_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u736 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u737 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u738_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u738 {
  border-width:0px;
  position:absolute;
  left:1982px;
  top:168px;
  width:57px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u738 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u738_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u739 {
  border-width:0px;
  position:absolute;
  left:2039px;
  top:168px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u739 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u739_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u740 {
  border-width:0px;
  position:absolute;
  left:2039px;
  top:200px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u740 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u741 {
  border-width:0px;
  position:absolute;
  left:1936px;
  top:144px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u741 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u741_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u742 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u743_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:813px;
}
#u743 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:1460px;
  height:813px;
  display:flex;
}
#u743 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u743_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1232px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u744 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:68px;
  width:1232px;
  height:64px;
  display:flex;
}
#u744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1367px;
  height:595px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u745 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:148px;
  width:1367px;
  height:595px;
  display:flex;
}
#u745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u745_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u746 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u747 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:207px;
  width:1593px;
  height:40px;
  display:flex;
}
#u747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u748_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u748 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:247px;
  width:1593px;
  height:40px;
  display:flex;
}
#u748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u749 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:286px;
  width:1593px;
  height:40px;
  display:flex;
}
#u749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u750 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:326px;
  width:1593px;
  height:40px;
  display:flex;
}
#u750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u751 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:366px;
  width:1593px;
  height:40px;
  display:flex;
}
#u751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u752_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u752 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:406px;
  width:1593px;
  height:40px;
  display:flex;
}
#u752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u753 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:446px;
  width:1593px;
  height:40px;
  display:flex;
}
#u753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u754_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u754 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:486px;
  width:1593px;
  height:40px;
  display:flex;
}
#u754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u755_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u755 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:526px;
  width:1593px;
  height:40px;
  display:flex;
}
#u755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u756_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u756 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:566px;
  width:1593px;
  height:40px;
  display:flex;
}
#u756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u757_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1593px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u757 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:606px;
  width:1593px;
  height:40px;
  display:flex;
}
#u757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u758_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:43px;
}
#u758 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:254px;
  width:172px;
  height:43px;
  display:flex;
}
#u758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:516px;
  height:36px;
}
#u759 {
  border-width:0px;
  position:absolute;
  left:914px;
  top:684px;
  width:516px;
  height:36px;
  display:flex;
}
#u759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u760_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
}
#u760 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
  display:flex;
}
#u760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u761 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:32px;
}
#u762 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:11px;
  width:121px;
  height:32px;
  display:flex;
}
#u762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u763_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u763 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:18px;
  width:19px;
  height:19px;
  display:flex;
}
#u763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u764_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u764 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:91px;
  width:24px;
  height:24px;
  display:flex;
}
#u764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u765 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:314px;
  width:24px;
  height:24px;
  display:flex;
}
#u765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u766 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:313px;
  width:24px;
  height:24px;
  display:flex;
}
#u766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u767_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u767 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:90px;
  width:24px;
  height:24px;
  display:flex;
}
#u767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u768 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:143px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u768 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u768_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u769_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u769 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:17px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u769 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u769_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u770 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u771_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u771 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:90px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u771 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u771_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u772_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u772 {
  border-width:0px;
  position:absolute;
  left:298px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u772 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u773 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:167px;
  width:129px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u773 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u773_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u774_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u774 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:91px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u774 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u774_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u775 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:184px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u775 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u775_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u776_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u776 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:224px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u776 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u776_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u777 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:314px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u777 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u777_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u778_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u778 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u778 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u778_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u779_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u779 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:247px;
  width:114px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u779 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u779_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u780_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u780 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u780 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u780_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u781 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:247px;
  width:47px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u781 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u781_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u782_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u782 {
  border-width:0px;
  position:absolute;
  left:906px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u782 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u782_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u783 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u783 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u783_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u784 {
  border-width:0px;
  position:absolute;
  left:660px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u784 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u784_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u785 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u786 {
  border-width:0px;
  position:absolute;
  left:1429px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u786 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u786_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:10px;
}
#u787 {
  border-width:0px;
  position:absolute;
  left:1489px;
  top:221px;
  width:7px;
  height:10px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  line-height:18px;
}
#u787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u788_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u788 {
  border-width:0px;
  position:absolute;
  left:1760px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u788 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u788_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u789_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u789 {
  border-width:0px;
  position:absolute;
  left:1744px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u789 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u789_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u790 {
  border-width:0px;
  position:absolute;
  left:874px;
  top:247px;
  width:92px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u790 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u790_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u791 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:247px;
  width:85px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u791 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u791_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u792_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u792 {
  border-width:0px;
  position:absolute;
  left:612px;
  top:247px;
  width:125px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u792 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u792_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u793 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u793 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u793_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:21px;
}
#u794 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:368px;
  width:24px;
  height:21px;
  display:flex;
}
#u794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u795 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:368px;
  width:24px;
  height:24px;
  display:flex;
}
#u795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u796_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u796 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:368px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u796 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u796_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u797_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u797 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:265px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u797 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u797_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u798 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u799 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:90px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u799 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u799_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u800 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u800 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u801 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:94px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u801 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u802 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u803 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u804 {
  border-width:0px;
  position:absolute;
  left:1240px;
  top:84px;
  width:61px;
  height:32px;
  display:flex;
}
#u804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u805 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u805 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u805_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u806 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u807 {
  border-width:0px;
  position:absolute;
  left:1313px;
  top:84px;
  width:52px;
  height:32px;
  display:flex;
}
#u807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u808_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u808 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u808 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u808_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u809_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u809 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u809 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u809_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u810 {
  border-width:0px;
  position:absolute;
  left:1406px;
  top:88px;
  width:24px;
  height:24px;
  display:flex;
}
#u810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u811 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:247px;
  width:85px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u811 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u811_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u812 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u812 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u812_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u813 {
  border-width:0px;
  position:absolute;
  left:785px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u813 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u813_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u814 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:247px;
  width:125px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u814 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u814_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u815_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u815 {
  border-width:0px;
  position:absolute;
  left:982px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u815 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u815_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u816 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:247px;
  width:41px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u816 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u816_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u817_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u817 {
  border-width:0px;
  position:absolute;
  left:1054px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u817 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u817_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u818 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:247px;
  width:89px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u818 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u818_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u819 {
  border-width:0px;
  position:absolute;
  left:1653px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u819 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u819_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u820 {
  border-width:0px;
  position:absolute;
  left:1637px;
  top:247px;
  width:89px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u820 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u820_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u821 {
  border-width:0px;
  position:absolute;
  left:1565px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u821 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u821_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u822 {
  border-width:0px;
  position:absolute;
  left:1563px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u822 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u822_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u823 {
  border-width:0px;
  position:absolute;
  left:1318px;
  top:216px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u823 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u823_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u824 {
  border-width:0px;
  position:absolute;
  left:1302px;
  top:247px;
  width:75px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u824 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u824_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u825 {
  border-width:0px;
  position:absolute;
  left:1209px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u825 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u825_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u826_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u826 {
  border-width:0px;
  position:absolute;
  left:1172px;
  top:247px;
  width:103px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u826 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u826_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
