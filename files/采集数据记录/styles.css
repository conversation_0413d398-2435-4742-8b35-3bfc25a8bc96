﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:3363px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1261px;
  height:234px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(233, 233, 233, 1);
  border-left:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:2078px;
  top:483px;
  width:1261px;
  height:234px;
  display:flex;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:2601px;
  top:517px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u5 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:2840px;
  top:527px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u6 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:2601px;
  top:549px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:2530px;
  top:517px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u8 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u11_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:2125px;
  top:517px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u11 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:2196px;
  top:517px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u12 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:2196px;
  top:549px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u13 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u14_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:2093px;
  top:493px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u17_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:3033px;
  top:517px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u17 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:3033px;
  top:549px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u19_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:2962px;
  top:517px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u19 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u19_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u22_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:2601px;
  top:584px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u22 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u22_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u23_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:2601px;
  top:616px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u24_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:2545px;
  top:584px;
  width:56px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u24 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:3033px;
  top:584px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u27 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:3033px;
  top:616px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:2934px;
  top:584px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u29 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u32_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:2125px;
  top:584px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u32 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:2196px;
  top:584px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:2196px;
  top:616px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u34 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:2093px;
  top:560px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u35 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:2196px;
  top:646px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u38 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:2196px;
  top:678px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u39 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:2097px;
  top:646px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u40 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u43_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:2601px;
  top:646px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u44_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:2601px;
  top:678px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u44 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u44_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u45_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:2502px;
  top:646px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u45 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u46_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1270px;
  height:54px;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:2093px;
  top:380px;
  width:1270px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF3399;
  text-align:left;
  line-height:18px;
}
#u46 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:2614px;
  top:2923px;
  width:32px;
  height:32px;
}
#u47_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u47_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u48_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:377px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:22px;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:2093px;
  top:22px;
  width:377px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:22px;
}
#u48 .text {
  position:absolute;
  align-self:center;
  padding:16px 0px 16px 32px;
  box-sizing:border-box;
  width:100%;
}
#u48_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u50_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1159px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(233, 233, 233, 1);
  border-left:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:2115px;
  top:202px;
  width:1159px;
  height:100px;
  display:flex;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u50 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u50_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u53_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:2588px;
  top:236px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u53 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u54_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:2827px;
  top:246px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u54 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u55_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:2588px;
  top:268px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u55 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u55_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u56_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:2517px;
  top:236px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u56 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u56_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u59_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:2162px;
  top:236px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u59 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u59_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u60_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:2233px;
  top:236px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u60 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u60_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u61_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:2233px;
  top:268px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u61 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u61_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u62_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:2130px;
  top:212px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u62 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u62_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u65_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:3084px;
  top:236px;
  width:61px;
  height:32px;
  display:flex;
}
#u65 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u65_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u66_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:3100px;
  top:242px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u66 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u66_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u68_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:3157px;
  top:236px;
  width:52px;
  height:32px;
  display:flex;
}
#u68 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u68_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u69_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:3169px;
  top:242px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u69 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u69_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u70_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:3221px;
  top:242px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u70 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u71_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:3250px;
  top:240px;
  width:24px;
  height:24px;
  display:flex;
}
#u71 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u71_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u72_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:813px;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:1460px;
  height:813px;
  display:flex;
}
#u72 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u72_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u73_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1232px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:68px;
  width:1232px;
  height:64px;
  display:flex;
}
#u73 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u74_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1367px;
  height:595px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:148px;
  width:1367px;
  height:595px;
  display:flex;
}
#u74 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u74_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u76_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:207px;
  width:1403px;
  height:40px;
  display:flex;
}
#u76 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u76_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u77_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:247px;
  width:1403px;
  height:40px;
  display:flex;
}
#u77 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u78_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:286px;
  width:1403px;
  height:40px;
  display:flex;
}
#u78 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u79_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:326px;
  width:1403px;
  height:40px;
  display:flex;
}
#u79 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u79_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u80_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:366px;
  width:1403px;
  height:40px;
  display:flex;
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u81_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:406px;
  width:1403px;
  height:40px;
  display:flex;
}
#u81 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u82_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:446px;
  width:1403px;
  height:40px;
  display:flex;
}
#u82 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u82_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:486px;
  width:1403px;
  height:40px;
  display:flex;
}
#u83 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u83_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u84_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:526px;
  width:1403px;
  height:40px;
  display:flex;
}
#u84 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u84_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u85_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:566px;
  width:1403px;
  height:40px;
  display:flex;
}
#u85 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u85_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u86_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1403px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:606px;
  width:1403px;
  height:40px;
  display:flex;
}
#u86 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u87_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:43px;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:132px;
  width:172px;
  height:43px;
  display:flex;
}
#u87 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u87_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u88_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:516px;
  height:36px;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:914px;
  top:684px;
  width:516px;
  height:36px;
  display:flex;
}
#u88 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u88_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u89_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
  display:flex;
}
#u89 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u89_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u91_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:32px;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:11px;
  width:121px;
  height:32px;
  display:flex;
}
#u91 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u91_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u92_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:18px;
  width:19px;
  height:19px;
  display:flex;
}
#u92 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u92_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u93_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:91px;
  width:24px;
  height:24px;
  display:flex;
}
#u93 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u93_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u94_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:314px;
  width:24px;
  height:24px;
  display:flex;
}
#u94 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u94_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u95_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:313px;
  width:24px;
  height:24px;
  display:flex;
}
#u95 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u95_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u96_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:90px;
  width:24px;
  height:24px;
  display:flex;
}
#u96 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u96_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u97_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:143px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u97 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u97_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u98_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u98 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:17px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u98 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u98_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u99 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u100 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:90px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u100 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u100_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u101_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u101 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u101 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u102 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:167px;
  width:129px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u102 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u102_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u103_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u103 {
  border-width:0px;
  position:absolute;
  left:1249px;
  top:167px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u103 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u103_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u104_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u104 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:91px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u104 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u104_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u105_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u105 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:184px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u105 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u105_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u106_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u106 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:224px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u106 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u106_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u107_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u107 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:314px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u107 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u107_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u108 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u108 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u108_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:174px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u109 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:247px;
  width:174px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u109 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u109_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u110 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:216px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u110 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u110_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u111_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u111 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:247px;
  width:208px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u111 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u111_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u112 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u112 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u112_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u113 {
  border-width:0px;
  position:absolute;
  left:977px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u113 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u113_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u114_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u114 {
  border-width:0px;
  position:absolute;
  left:647px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u114 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u114_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u115 {
  border-width:0px;
  position:absolute;
  left:732px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u115 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u115_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u116 {
  border-width:0px;
  position:absolute;
  left:1302px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u116 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u116_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u117 {
  border-width:0px;
  position:absolute;
  left:1123px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u117 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u117_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u118 {
  border-width:0px;
  position:absolute;
  left:1576px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u118 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u118_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u119 {
  border-width:0px;
  position:absolute;
  left:1560px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u119 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u119_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u120 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:247px;
  width:125px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u120 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u120_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u121 {
  border-width:0px;
  position:absolute;
  left:947px;
  top:247px;
  width:117px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u121 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u121_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u122 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:247px;
  width:82px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u122 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u122_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u123 {
  border-width:0px;
  position:absolute;
  left:730px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u123 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u123_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:1259px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u124 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u124_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u125 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:247px;
  width:166px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u125_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u126_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:21px;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:368px;
  width:24px;
  height:21px;
  display:flex;
}
#u126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u127_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:368px;
  width:24px;
  height:24px;
  display:flex;
}
#u127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:368px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u128 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u128_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:265px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u129 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u129_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:90px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u131 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u131_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u132_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u132 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u133_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:94px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u133 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u135 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u136_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u136 {
  border-width:0px;
  position:absolute;
  left:1240px;
  top:84px;
  width:61px;
  height:32px;
  display:flex;
}
#u136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u137 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u137 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u137_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u138 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u139_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u139 {
  border-width:0px;
  position:absolute;
  left:1313px;
  top:84px;
  width:52px;
  height:32px;
  display:flex;
}
#u139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u140 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u140 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u140_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u141 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u141 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u141_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u142_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u142 {
  border-width:0px;
  position:absolute;
  left:1406px;
  top:88px;
  width:24px;
  height:24px;
  display:flex;
}
#u142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u143 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u144 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u145_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u145 {
  border-width:0px;
  position:absolute;
  left:3116px;
  top:646px;
  width:61px;
  height:32px;
  display:flex;
}
#u145 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u146_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u146 {
  border-width:0px;
  position:absolute;
  left:3132px;
  top:652px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u146 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u146_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u147 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u148_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u148 {
  border-width:0px;
  position:absolute;
  left:3189px;
  top:646px;
  width:52px;
  height:32px;
  display:flex;
}
#u148 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u149_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u149 {
  border-width:0px;
  position:absolute;
  left:3201px;
  top:652px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u149 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u149_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u150 {
  border-width:0px;
  position:absolute;
  left:3253px;
  top:652px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u150 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u150_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u151_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u151 {
  border-width:0px;
  position:absolute;
  left:3282px;
  top:650px;
  width:24px;
  height:24px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:10px;
}
#u152 {
  border-width:0px;
  position:absolute;
  left:561px;
  top:221px;
  width:7px;
  height:10px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  line-height:18px;
}
#u152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u153_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:10px;
}
#u153 {
  border-width:0px;
  position:absolute;
  left:1211px;
  top:221px;
  width:7px;
  height:10px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  line-height:18px;
}
#u153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:10px;
}
#u154 {
  border-width:0px;
  position:absolute;
  left:1390px;
  top:221px;
  width:7px;
  height:10px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  line-height:18px;
}
#u154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:946px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.6);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u155 {
  border-width:0px;
  position:absolute;
  left:1802px;
  top:944px;
  width:1024px;
  height:946px;
  display:flex;
}
#u155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:864px;
  height:726px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  -moz-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u156 {
  border-width:0px;
  position:absolute;
  left:1882px;
  top:1056px;
  width:864px;
  height:726px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u156 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u157 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:864px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u158 {
  border-width:0px;
  position:absolute;
  left:1882px;
  top:1056px;
  width:864px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u158 .text {
  position:absolute;
  align-self:center;
  padding:16px 24px 16px 24px;
  box-sizing:border-box;
  width:100%;
}
#u158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u159 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u160_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u160 {
  border-width:0px;
  position:absolute;
  left:2706px;
  top:1077px;
  width:12px;
  height:12px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u161 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u162 {
  border-width:0px;
  position:absolute;
  left:1948px;
  top:1134px;
  width:56px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u162_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u163 {
  border-width:0px;
  position:absolute;
  left:2013px;
  top:1134px;
  width:311px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u163 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u164 {
  border-width:0px;
  position:absolute;
  left:1933px;
  top:1156px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u165 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u166 {
  border-width:0px;
  position:absolute;
  left:1933px;
  top:1172px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u166 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u166_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u167 {
  border-width:0px;
  position:absolute;
  left:2013px;
  top:1172px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u167 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u168 {
  border-width:0px;
  position:absolute;
  left:1933px;
  top:1194px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u169 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u170 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:650px;
  height:258px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u171 {
  border-width:0px;
  position:absolute;
  left:2009px;
  top:1301px;
  width:650px;
  height:258px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u171 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:5px;
}
#u172 {
  border-width:0px;
  position:absolute;
  left:2639px;
  top:1549px;
  width:14px;
  height:5px;
  display:flex;
  -webkit-transform:rotate(315deg);
  -moz-transform:rotate(315deg);
  -ms-transform:rotate(315deg);
  transform:rotate(315deg);
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 8px 2px;
  box-sizing:border-box;
  width:100%;
}
#u172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u173 {
  border-width:0px;
  position:absolute;
  left:1905px;
  top:1301px;
  width:104px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u173 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u173_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u174 {
  border-width:0px;
  position:absolute;
  left:2230px;
  top:1548px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u174 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u175 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u176 {
  border-width:0px;
  position:absolute;
  left:2364px;
  top:1134px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u176 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u176_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u177 {
  border-width:0px;
  position:absolute;
  left:2435px;
  top:1134px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u177 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u178 {
  border-width:0px;
  position:absolute;
  left:2364px;
  top:1156px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u178 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u179 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u180 {
  border-width:0px;
  position:absolute;
  left:2364px;
  top:1172px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u180 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u180_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u181 {
  border-width:0px;
  position:absolute;
  left:2435px;
  top:1172px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u181 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u182 {
  border-width:0px;
  position:absolute;
  left:2364px;
  top:1194px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u183 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:864px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-left:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:4px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u184 {
  border-width:0px;
  position:absolute;
  left:1882px;
  top:1718px;
  width:864px;
  height:64px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u184 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:32px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u185 {
  border-width:0px;
  position:absolute;
  left:2657px;
  top:1734px;
  width:66px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u185 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u185_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:21px;
}
#u186 {
  border-width:0px;
  position:absolute;
  left:2587px;
  top:1734px;
  width:66px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:21px;
}
#u186 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u186_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u187 {
  border-width:0px;
  position:absolute;
  left:2665px;
  top:1718px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u188_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u188 {
  border-width:0px;
  position:absolute;
  left:2665px;
  top:1766px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u189 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u190 {
  border-width:0px;
  position:absolute;
  left:1932px;
  top:1210px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u190 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u190_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u191 {
  border-width:0px;
  position:absolute;
  left:2013px;
  top:1210px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u191 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u192 {
  border-width:0px;
  position:absolute;
  left:1942px;
  top:1232px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u193 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u194 {
  border-width:0px;
  position:absolute;
  left:2364px;
  top:1210px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u194 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u194_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u195 {
  border-width:0px;
  position:absolute;
  left:2435px;
  top:1210px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u195 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u196 {
  border-width:0px;
  position:absolute;
  left:2374px;
  top:1232px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u197 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u198 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:650px;
  height:79px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:22px;
}
#u199 {
  border-width:0px;
  position:absolute;
  left:2009px;
  top:1584px;
  width:650px;
  height:79px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:22px;
}
#u199 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:5px;
}
#u200 {
  border-width:0px;
  position:absolute;
  left:2639px;
  top:1653px;
  width:14px;
  height:5px;
  display:flex;
  -webkit-transform:rotate(315deg);
  -moz-transform:rotate(315deg);
  -ms-transform:rotate(315deg);
  transform:rotate(315deg);
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 8px 2px;
  box-sizing:border-box;
  width:100%;
}
#u200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u201 {
  border-width:0px;
  position:absolute;
  left:1933px;
  top:1584px;
  width:76px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u201 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u201_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u202_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u202 {
  border-width:0px;
  position:absolute;
  left:2230px;
  top:1652px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u202 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u203 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u204_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u204 {
  border-width:0px;
  position:absolute;
  left:2336px;
  top:1253px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u204 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u204_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u205 {
  border-width:0px;
  position:absolute;
  left:2435px;
  top:1253px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u205 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u206 {
  border-width:0px;
  position:absolute;
  left:2374px;
  top:1275px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u207 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u208 {
  border-width:0px;
  position:absolute;
  left:1905px;
  top:1253px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u208 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u208_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u209 {
  border-width:0px;
  position:absolute;
  left:2004px;
  top:1253px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u209 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u210 {
  border-width:0px;
  position:absolute;
  left:1943px;
  top:1275px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u211 {
  border-width:0px;
  position:absolute;
  left:1466px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u211 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u211_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u212 {
  border-width:0px;
  position:absolute;
  left:1445px;
  top:247px;
  width:100px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u212 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u212_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
