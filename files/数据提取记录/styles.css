﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:2968px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u520 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u521_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1261px;
  height:234px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(233, 233, 233, 1);
  border-left:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u521 {
  border-width:0px;
  position:absolute;
  left:1683px;
  top:500px;
  width:1261px;
  height:234px;
  display:flex;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u521 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u522 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u523 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u524_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u524 {
  border-width:0px;
  position:absolute;
  left:2206px;
  top:534px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u524 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u525_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u525 {
  border-width:0px;
  position:absolute;
  left:2445px;
  top:544px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u525 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u525_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u526 {
  border-width:0px;
  position:absolute;
  left:2206px;
  top:566px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u526 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u527 {
  border-width:0px;
  position:absolute;
  left:2135px;
  top:534px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u527 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u527_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u528 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u529 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u530 {
  border-width:0px;
  position:absolute;
  left:1758px;
  top:534px;
  width:43px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u530 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u530_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u531 {
  border-width:0px;
  position:absolute;
  left:1801px;
  top:534px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u531 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u532 {
  border-width:0px;
  position:absolute;
  left:1801px;
  top:566px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u532 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u532_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u533 {
  border-width:0px;
  position:absolute;
  left:1698px;
  top:510px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u534 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u535 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u536 {
  border-width:0px;
  position:absolute;
  left:2638px;
  top:534px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u536 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u537 {
  border-width:0px;
  position:absolute;
  left:2638px;
  top:566px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u537 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u537_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u538 {
  border-width:0px;
  position:absolute;
  left:2567px;
  top:534px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u538 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u538_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u539 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u540 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u541 {
  border-width:0px;
  position:absolute;
  left:2638px;
  top:601px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u541 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u541_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u542 {
  border-width:0px;
  position:absolute;
  left:2638px;
  top:633px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u542 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u543_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u543 {
  border-width:0px;
  position:absolute;
  left:2539px;
  top:601px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u543 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u543_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u544 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u545 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u546_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u546 {
  border-width:0px;
  position:absolute;
  left:1801px;
  top:601px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u546 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u547_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u547 {
  border-width:0px;
  position:absolute;
  left:1801px;
  top:633px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u547 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u548 {
  border-width:0px;
  position:absolute;
  left:1730px;
  top:601px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u548 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u548_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u549 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u550 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u551_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u551 {
  border-width:0px;
  position:absolute;
  left:2206px;
  top:601px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u551 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u552_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u552 {
  border-width:0px;
  position:absolute;
  left:2206px;
  top:633px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u552 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u552_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u553_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u553 {
  border-width:0px;
  position:absolute;
  left:2135px;
  top:601px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u553 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u553_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u554_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1270px;
  height:54px;
}
#u554 {
  border-width:0px;
  position:absolute;
  left:1698px;
  top:397px;
  width:1270px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF3399;
  text-align:left;
  line-height:18px;
}
#u554 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u554_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u555_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:377px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:22px;
}
#u555 {
  border-width:0px;
  position:absolute;
  left:1698px;
  top:39px;
  width:377px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:22px;
}
#u555 .text {
  position:absolute;
  align-self:center;
  padding:16px 0px 16px 32px;
  box-sizing:border-box;
  width:100%;
}
#u555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u556 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u557_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1159px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(233, 233, 233, 1);
  border-left:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u557 {
  border-width:0px;
  position:absolute;
  left:1720px;
  top:219px;
  width:1159px;
  height:100px;
  display:flex;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u557 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u557_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u558 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u559 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u560 {
  border-width:0px;
  position:absolute;
  left:2193px;
  top:253px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u560 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u560_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u561_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u561 {
  border-width:0px;
  position:absolute;
  left:2432px;
  top:263px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u561 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u562 {
  border-width:0px;
  position:absolute;
  left:2193px;
  top:285px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u562 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u562_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u563_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u563 {
  border-width:0px;
  position:absolute;
  left:2122px;
  top:253px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u563 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u563_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u564 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u565 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u566 {
  border-width:0px;
  position:absolute;
  left:1795px;
  top:253px;
  width:43px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u566 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u566_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u567 {
  border-width:0px;
  position:absolute;
  left:1838px;
  top:253px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u567 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u568 {
  border-width:0px;
  position:absolute;
  left:1838px;
  top:285px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u568 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u569 {
  border-width:0px;
  position:absolute;
  left:1735px;
  top:229px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u570 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u571 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u572_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u572 {
  border-width:0px;
  position:absolute;
  left:2689px;
  top:253px;
  width:61px;
  height:32px;
  display:flex;
}
#u572 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u573_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u573 {
  border-width:0px;
  position:absolute;
  left:2705px;
  top:259px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u573 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u573_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u574 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u575_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u575 {
  border-width:0px;
  position:absolute;
  left:2762px;
  top:253px;
  width:52px;
  height:32px;
  display:flex;
}
#u575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u576 {
  border-width:0px;
  position:absolute;
  left:2774px;
  top:259px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u576 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u576_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u577 {
  border-width:0px;
  position:absolute;
  left:2826px;
  top:259px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u577 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u577_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u578_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u578 {
  border-width:0px;
  position:absolute;
  left:2855px;
  top:257px;
  width:24px;
  height:24px;
  display:flex;
}
#u578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u579_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:813px;
}
#u579 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:1460px;
  height:813px;
  display:flex;
}
#u579 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u580_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1232px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u580 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:68px;
  width:1232px;
  height:64px;
  display:flex;
}
#u580 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1234px;
  height:595px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u581 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:148px;
  width:1234px;
  height:595px;
  display:flex;
}
#u581 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u582 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u583_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u583 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:207px;
  width:1198px;
  height:40px;
  display:flex;
}
#u583 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u584_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u584 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:247px;
  width:1198px;
  height:40px;
  display:flex;
}
#u584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u585 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:286px;
  width:1198px;
  height:40px;
  display:flex;
}
#u585 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u586 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:326px;
  width:1198px;
  height:40px;
  display:flex;
}
#u586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u587 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:366px;
  width:1198px;
  height:40px;
  display:flex;
}
#u587 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u588_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u588 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:406px;
  width:1198px;
  height:40px;
  display:flex;
}
#u588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u589 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:446px;
  width:1198px;
  height:40px;
  display:flex;
}
#u589 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u590 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:486px;
  width:1198px;
  height:40px;
  display:flex;
}
#u590 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u591 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:526px;
  width:1198px;
  height:40px;
  display:flex;
}
#u591 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u592 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:566px;
  width:1198px;
  height:40px;
  display:flex;
}
#u592 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1198px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u593 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:606px;
  width:1198px;
  height:40px;
  display:flex;
}
#u593 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u593_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u594_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:43px;
}
#u594 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:213px;
  width:172px;
  height:43px;
  display:flex;
}
#u594 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u594_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u595_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:516px;
  height:36px;
}
#u595 {
  border-width:0px;
  position:absolute;
  left:914px;
  top:684px;
  width:516px;
  height:36px;
  display:flex;
}
#u595 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u596_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
}
#u596 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
  display:flex;
}
#u596 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u597 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u598_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:32px;
}
#u598 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:11px;
  width:121px;
  height:32px;
  display:flex;
}
#u598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u599_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u599 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:18px;
  width:19px;
  height:19px;
  display:flex;
}
#u599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u600_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u600 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:91px;
  width:24px;
  height:24px;
  display:flex;
}
#u600 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u601_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u601 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:314px;
  width:24px;
  height:24px;
  display:flex;
}
#u601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u602_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u602 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:313px;
  width:24px;
  height:24px;
  display:flex;
}
#u602 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u603_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u603 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:90px;
  width:24px;
  height:24px;
  display:flex;
}
#u603 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u604 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:143px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u604 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u604_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u605_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u605 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:17px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u605 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u605_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u606 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u607 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u607 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u607_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u608_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u608 {
  border-width:0px;
  position:absolute;
  left:284px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u608 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u609 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:167px;
  width:129px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u609 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u609_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u610 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:91px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u610 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u610_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u611_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u611 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:224px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u611 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u611_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u612_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u612 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:314px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u612 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u612_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u613_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u613 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u613 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u613_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u614_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u614 {
  border-width:0px;
  position:absolute;
  left:664px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u614 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u614_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u615_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u615 {
  border-width:0px;
  position:absolute;
  left:503px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u615 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u615_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u616_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u616 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u616 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u616_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u617_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u617 {
  border-width:0px;
  position:absolute;
  left:1263px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u617 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u617_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u618_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u618 {
  border-width:0px;
  position:absolute;
  left:819px;
  top:216px;
  width:79px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u618 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u618_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u619_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u619 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:247px;
  width:131px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u619 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u619_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u620_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u620 {
  border-width:0px;
  position:absolute;
  left:621px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u620 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u620_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u621_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u621 {
  border-width:0px;
  position:absolute;
  left:448px;
  top:247px;
  width:166px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u621 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u621_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u622 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:247px;
  width:51px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u622 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u622_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u623_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:21px;
}
#u623 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:368px;
  width:24px;
  height:21px;
  display:flex;
}
#u623 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u624 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:368px;
  width:24px;
  height:24px;
  display:flex;
}
#u624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u625_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u625 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:368px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u625 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u625_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u626 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:265px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u626 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u626_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u627 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u628_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u628 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:90px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u628 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u628_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u629_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u629 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u629 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u629_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u630_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u630 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:94px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u630 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u631 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u632 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u633_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u633 {
  border-width:0px;
  position:absolute;
  left:1240px;
  top:84px;
  width:61px;
  height:32px;
  display:flex;
}
#u633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u634 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u634 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u634_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u635 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u636_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u636 {
  border-width:0px;
  position:absolute;
  left:1313px;
  top:84px;
  width:52px;
  height:32px;
  display:flex;
}
#u636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u636_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u637 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u637 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u637_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u638 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u638 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u638_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u639 {
  border-width:0px;
  position:absolute;
  left:1406px;
  top:88px;
  width:24px;
  height:24px;
  display:flex;
}
#u639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u640 {
  border-width:0px;
  position:absolute;
  left:1185px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u640 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u640_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u641 {
  border-width:0px;
  position:absolute;
  left:1164px;
  top:247px;
  width:100px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u641 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u641_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u642 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u643 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u644_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u644 {
  border-width:0px;
  position:absolute;
  left:2721px;
  top:663px;
  width:61px;
  height:32px;
  display:flex;
}
#u644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u645 {
  border-width:0px;
  position:absolute;
  left:2737px;
  top:669px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u645 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u645_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u646 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u647_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u647 {
  border-width:0px;
  position:absolute;
  left:2794px;
  top:663px;
  width:52px;
  height:32px;
  display:flex;
}
#u647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u648 {
  border-width:0px;
  position:absolute;
  left:2806px;
  top:669px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u648 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u648_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u649 {
  border-width:0px;
  position:absolute;
  left:2858px;
  top:669px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u649 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u649_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u650 {
  border-width:0px;
  position:absolute;
  left:2887px;
  top:667px;
  width:24px;
  height:24px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u651_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u651 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:184px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u651 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u651_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1554px;
  height:946px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.6);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u652 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:1176px;
  width:1554px;
  height:946px;
  display:flex;
}
#u652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1355px;
  height:694px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  -moz-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u653 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:1284px;
  width:1355px;
  height:694px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u653 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1355px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u654 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:1284px;
  width:1355px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u654 .text {
  position:absolute;
  align-self:center;
  padding:16px 24px 16px 24px;
  box-sizing:border-box;
  width:100%;
}
#u654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u655 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u656_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u656 {
  border-width:0px;
  position:absolute;
  left:1413px;
  top:1305px;
  width:12px;
  height:12px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u656 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1355px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-left:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:4px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u657 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:1914px;
  width:1355px;
  height:64px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u657 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:32px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u658 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:1930px;
  width:57px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u658 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u658_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u659 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:1946px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u659 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u659_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u660 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:1994px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u660 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u661_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1115px;
  height:146px;
}
#u661 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:935px;
  width:1115px;
  height:146px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
  text-align:left;
}
#u661 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u661_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u662_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u662 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u662 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u662_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u663 {
  border-width:0px;
  position:absolute;
  left:926px;
  top:247px;
  width:41px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u663 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u663_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u664 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u664 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u664_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u665 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:247px;
  width:41px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u665 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u665_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u666 {
  border-width:0px;
  position:absolute;
  left:1324px;
  top:247px;
  width:101px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u666 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u666_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u667 {
  border-width:0px;
  position:absolute;
  left:1036px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u667 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u667_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u668 {
  border-width:0px;
  position:absolute;
  left:993px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u668 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u668_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u669 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u670 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u671 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:331px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F0F7FF;
  text-align:left;
  line-height:22px;
}
#u672 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:1559px;
  width:1201px;
  height:331px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F0F7FF;
  text-align:left;
  line-height:22px;
}
#u672 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u673 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u674 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u675 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1580px;
  width:1163px;
  height:40px;
  display:flex;
}
#u675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u676 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1620px;
  width:1163px;
  height:40px;
  display:flex;
}
#u676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u677 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1659px;
  width:1163px;
  height:40px;
  display:flex;
}
#u677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u678 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1699px;
  width:1163px;
  height:40px;
  display:flex;
}
#u678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u679 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1739px;
  width:1163px;
  height:40px;
  display:flex;
}
#u679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u680 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1779px;
  width:1163px;
  height:40px;
  display:flex;
}
#u680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1163px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u681 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1819px;
  width:1163px;
  height:40px;
  display:flex;
}
#u681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u682 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u683 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:1589px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u683 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u683_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:239px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u684 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:1620px;
  width:208px;
  height:239px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u684 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u684_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u685 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u686 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:1589px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u686 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u686_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:239px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u687 {
  border-width:0px;
  position:absolute;
  left:712px;
  top:1620px;
  width:125px;
  height:239px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u687 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u687_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u688 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u689 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:1589px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u689 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u689_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:239px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u690 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:1620px;
  width:80px;
  height:239px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u690 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u690_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u691 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u692 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:1589px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u692 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u692_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:239px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u693 {
  border-width:0px;
  position:absolute;
  left:598px;
  top:1620px;
  width:61px;
  height:239px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u693 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u693_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u694 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u695 {
  border-width:0px;
  position:absolute;
  left:1158px;
  top:1589px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u695 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u695_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u696_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:174px;
  height:239px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u696 {
  border-width:0px;
  position:absolute;
  left:1113px;
  top:1620px;
  width:174px;
  height:239px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u696 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u696_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u697 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u698 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:1589px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u698 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u698_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:239px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u699 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:1620px;
  width:168px;
  height:239px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u699 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u699_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u700 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u701 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1201px;
  height:106px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F0F7FF;
  text-align:left;
  line-height:22px;
}
#u702 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:1391px;
  width:1201px;
  height:106px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F0F7FF;
  text-align:left;
  line-height:22px;
}
#u702 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u703 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:1359px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u703 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u703_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u704 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:1527px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u704 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u704_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u705 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u706 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:1418px;
  width:43px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u706 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u706_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u707_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u707 {
  border-width:0px;
  position:absolute;
  left:956px;
  top:1418px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u707 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u708 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:1440px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u709 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u710_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u710 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1456px;
  width:43px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u710 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u710_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u711 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1456px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u711 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u712 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1478px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u713 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u714_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u714 {
  border-width:0px;
  position:absolute;
  left:546px;
  top:1456px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u714 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u714_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u715_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u715 {
  border-width:0px;
  position:absolute;
  left:617px;
  top:1456px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u715 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u715_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u716 {
  border-width:0px;
  position:absolute;
  left:556px;
  top:1478px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u717 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u718 {
  border-width:0px;
  position:absolute;
  left:546px;
  top:1418px;
  width:93px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u718 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u718_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u719 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:1418px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u719 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u720_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u720 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:1440px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u721 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u722 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:1418px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u722 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u722_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u723 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:1418px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u723 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u724_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u724 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:1440px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u725 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u726 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:1456px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u726 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u726_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u727_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u727 {
  border-width:0px;
  position:absolute;
  left:975px;
  top:1456px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u727 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u728_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u728 {
  border-width:0px;
  position:absolute;
  left:914px;
  top:1478px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u728 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
