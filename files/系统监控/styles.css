﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-97px;
  width:832px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:832px;
  height:270px;
}
#u1050 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:129px;
  width:832px;
  height:270px;
  display:flex;
}
#u1050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1051_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:832px;
  height:230px;
}
#u1051 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:511px;
  width:832px;
  height:230px;
  display:flex;
}
#u1051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1052 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:466px;
  width:118px;
  height:18px;
  display:flex;
}
#u1052 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1052_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1053 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:84px;
  width:118px;
  height:18px;
  display:flex;
}
#u1053 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1053_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1054 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:805px;
  width:119px;
  height:18px;
  display:flex;
}
#u1054 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1054_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:830px;
  height:298px;
}
#u1055 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:842px;
  width:830px;
  height:298px;
  display:flex;
}
#u1055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
