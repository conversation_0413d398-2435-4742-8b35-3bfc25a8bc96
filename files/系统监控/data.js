﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,_(L,M,N,M),O,P,Q,null,R,S,T,U,V,W,X,S,Y,Z,ba,_(F,G,H,bb),bc,S,bd,Z,be,_(bf,bg,bh,bi,bj,bi,bk,bi,bl,k,H,_(bm,bn,bo,bn,bp,bn,bq,br)),i,_(j,k,l,k)),bs,_(),bt,_(),bu,_(bv,[_(bw,bx,by,h,bz,bA,u,bB,bC,bB,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,bQ,i,_(j,bR,l,bS),bT,_(bU,bV,bW,bX),J,null),bs,_(),bY,_(),bZ,_(ca,cb)),_(bw,cc,by,h,bz,bA,u,bB,bC,bB,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,bQ,i,_(j,bR,l,cd),bT,_(bU,bV,bW,ce),J,null),bs,_(),bY,_(),bZ,_(ca,cf)),_(bw,cg,by,h,bz,ch,u,ci,bC,ci,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,cj,i,_(j,ck,l,cl),bT,_(bU,bV,bW,cm)),bs,_(),bY,_(),cn,bg),_(bw,co,by,h,bz,ch,u,ci,bC,ci,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,cj,i,_(j,ck,l,cl),bT,_(bU,bV,bW,cp)),bs,_(),bY,_(),cn,bg),_(bw,cq,by,h,bz,ch,u,ci,bC,ci,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,cj,i,_(j,cr,l,cl),bT,_(bU,bV,bW,cs)),bs,_(),bY,_(),cn,bg),_(bw,ct,by,h,bz,bA,u,bB,bC,bB,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,bQ,i,_(j,cu,l,cv),bT,_(bU,bV,bW,cw),J,null),bs,_(),bY,_(),bZ,_(ca,cx))])),cy,_(),cz,_(cA,_(cB,cC),cD,_(cB,cE),cF,_(cB,cG),cH,_(cB,cI),cJ,_(cB,cK),cL,_(cB,cM)));}; 
var b="url",c="系统监控.html",d="generationDate",e=new Date(1748279432122.55),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="9657dc8f26e44dd88b3e3ed790666b56",u="type",v="Axure:Page",w="系统监控",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="horizontal",M="near",N="vertical",O="imageRepeat",P="auto",Q="favicon",R="sketchFactor",S="0",T="colorStyle",U="appliedColor",V="fontName",W="Applied Font",X="borderWidth",Y="borderVisibility",Z="all",ba="borderFill",bb=0xFF797979,bc="cornerRadius",bd="cornerVisibility",be="outerShadow",bf="on",bg=false,bh="offsetX",bi=5,bj="offsetY",bk="blurRadius",bl="spread",bm="r",bn=0,bo="g",bp="b",bq="a",br=0.349019607843137,bs="adaptiveStyles",bt="interactionMap",bu="diagram",bv="objects",bw="id",bx="c7f6071f39ea4ac9bf6e83b1a195678a",by="label",bz="friendlyType",bA="Image",bB="imageBox",bC="styleType",bD="visible",bE=true,bF="'ArialMT', 'Arial', sans-serif",bG="fontWeight",bH="400",bI="fontStyle",bJ="normal",bK="fontStretch",bL="5",bM="foreGroundFill",bN=0xFF333333,bO="opacity",bP=1,bQ="75a91ee5b9d042cfa01b8d565fe289c0",bR=832,bS=270,bT="location",bU="x",bV=97,bW="y",bX=129,bY="imageOverrides",bZ="images",ca="normal~",cb="images/系统监控/u1050.png",cc="f90f74bb9b6b469e9d2cf5306564703f",cd=230,ce=511,cf="images/系统监控/u1051.png",cg="267c81d1477e494492485affc5d3c1f5",ch="Rectangle",ci="vectorShape",cj="4988d43d80b44008a4a415096f1632af",ck=118,cl=18,cm=466,cn="generateCompound",co="5e8ffdf1a0d947998f7e0023b2d89b9e",cp=84,cq="395d4e6e469e41858e215c2e746977ff",cr=119,cs=805,ct="ea6854ed679144c29979589022ac5af1",cu=830,cv=298,cw=842,cx="images/系统监控/u1055.png",cy="masters",cz="objectPaths",cA="c7f6071f39ea4ac9bf6e83b1a195678a",cB="scriptId",cC="u1050",cD="f90f74bb9b6b469e9d2cf5306564703f",cE="u1051",cF="267c81d1477e494492485affc5d3c1f5",cG="u1052",cH="5e8ffdf1a0d947998f7e0023b2d89b9e",cI="u1053",cJ="395d4e6e469e41858e215c2e746977ff",cK="u1054",cL="ea6854ed679144c29979589022ac5af1",cM="u1055";
return _creator();
})());