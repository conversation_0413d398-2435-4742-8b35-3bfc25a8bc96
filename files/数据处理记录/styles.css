﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:3593px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1261px;
  height:302px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(233, 233, 233, 1);
  border-left:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u213 {
  border-width:0px;
  position:absolute;
  left:2308px;
  top:483px;
  width:1261px;
  height:302px;
  display:flex;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u213 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u214 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u215 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u216 {
  border-width:0px;
  position:absolute;
  left:2831px;
  top:517px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u216 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u217 {
  border-width:0px;
  position:absolute;
  left:3070px;
  top:527px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u217 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u218 {
  border-width:0px;
  position:absolute;
  left:2831px;
  top:549px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u219 {
  border-width:0px;
  position:absolute;
  left:2760px;
  top:517px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u219 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u219_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u220 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u221 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u222 {
  border-width:0px;
  position:absolute;
  left:2355px;
  top:517px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u222 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u222_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u223 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:517px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u223 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u224 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:549px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u225 {
  border-width:0px;
  position:absolute;
  left:2323px;
  top:493px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u227 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u228 {
  border-width:0px;
  position:absolute;
  left:2355px;
  top:646px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u228 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u228_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u229 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:646px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u229 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u230 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:678px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u231 {
  border-width:0px;
  position:absolute;
  left:2323px;
  top:622px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u232 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u233 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u234 {
  border-width:0px;
  position:absolute;
  left:2831px;
  top:646px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u234 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u235 {
  border-width:0px;
  position:absolute;
  left:2831px;
  top:678px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u236 {
  border-width:0px;
  position:absolute;
  left:2732px;
  top:646px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u236 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u236_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u237 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u238 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u239_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u239 {
  border-width:0px;
  position:absolute;
  left:3263px;
  top:646px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u239 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u240 {
  border-width:0px;
  position:absolute;
  left:3263px;
  top:678px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u240 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u241_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u241 {
  border-width:0px;
  position:absolute;
  left:3164px;
  top:646px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u241 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u241_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u242_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1270px;
  height:54px;
}
#u242 {
  border-width:0px;
  position:absolute;
  left:2323px;
  top:380px;
  width:1270px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF3399;
  text-align:left;
  line-height:18px;
}
#u242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:377px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:22px;
}
#u243 {
  border-width:0px;
  position:absolute;
  left:2323px;
  top:22px;
  width:377px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:22px;
}
#u243 .text {
  position:absolute;
  align-self:center;
  padding:16px 0px 16px 32px;
  box-sizing:border-box;
  width:100%;
}
#u243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u244 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1159px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(233, 233, 233, 1);
  border-left:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u245 {
  border-width:0px;
  position:absolute;
  left:2345px;
  top:202px;
  width:1159px;
  height:100px;
  display:flex;
  font-family:'MalayalamMN', 'Malayalam MN', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u245 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u246 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u247 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u248_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u248 {
  border-width:0px;
  position:absolute;
  left:2818px;
  top:236px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u248 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u249_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u249 {
  border-width:0px;
  position:absolute;
  left:3057px;
  top:246px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u249 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u250 {
  border-width:0px;
  position:absolute;
  left:2818px;
  top:268px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u251 {
  border-width:0px;
  position:absolute;
  left:2747px;
  top:236px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u251 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u251_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u252 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u253 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u254 {
  border-width:0px;
  position:absolute;
  left:2392px;
  top:236px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u254 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u254_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u255 {
  border-width:0px;
  position:absolute;
  left:2463px;
  top:236px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u255 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u256 {
  border-width:0px;
  position:absolute;
  left:2463px;
  top:268px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u257 {
  border-width:0px;
  position:absolute;
  left:2360px;
  top:212px;
  width:32px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u258 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u259 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u260_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u260 {
  border-width:0px;
  position:absolute;
  left:3314px;
  top:236px;
  width:61px;
  height:32px;
  display:flex;
}
#u260 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u261 {
  border-width:0px;
  position:absolute;
  left:3330px;
  top:242px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u261 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u261_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u262 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u263_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u263 {
  border-width:0px;
  position:absolute;
  left:3387px;
  top:236px;
  width:52px;
  height:32px;
  display:flex;
}
#u263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u264 {
  border-width:0px;
  position:absolute;
  left:3399px;
  top:242px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u264 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u265 {
  border-width:0px;
  position:absolute;
  left:3451px;
  top:242px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u265_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u266_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u266 {
  border-width:0px;
  position:absolute;
  left:3480px;
  top:240px;
  width:24px;
  height:24px;
  display:flex;
}
#u266 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u267_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:813px;
}
#u267 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:1460px;
  height:813px;
  display:flex;
}
#u267 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u267_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1232px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u268 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:68px;
  width:1232px;
  height:64px;
  display:flex;
}
#u268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1813px;
  height:595px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u269 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:148px;
  width:1813px;
  height:595px;
  display:flex;
}
#u269 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u270 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u271 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:207px;
  width:1825px;
  height:40px;
  display:flex;
}
#u271 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u272 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:247px;
  width:1825px;
  height:40px;
  display:flex;
}
#u272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u273 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:286px;
  width:1825px;
  height:40px;
  display:flex;
}
#u273 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u273_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u274 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:326px;
  width:1825px;
  height:40px;
  display:flex;
}
#u274 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u274_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u275 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:366px;
  width:1825px;
  height:40px;
  display:flex;
}
#u275 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u276 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:406px;
  width:1825px;
  height:40px;
  display:flex;
}
#u276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u276_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u277 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:446px;
  width:1825px;
  height:40px;
  display:flex;
}
#u277 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u278 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:486px;
  width:1825px;
  height:40px;
  display:flex;
}
#u278 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u279 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:526px;
  width:1825px;
  height:40px;
  display:flex;
}
#u279 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u279_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u280 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:566px;
  width:1825px;
  height:40px;
  display:flex;
}
#u280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u281_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1825px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u281 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:606px;
  width:1825px;
  height:40px;
  display:flex;
}
#u281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u282_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:43px;
}
#u282 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:173px;
  width:172px;
  height:43px;
  display:flex;
}
#u282 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:516px;
  height:36px;
}
#u283 {
  border-width:0px;
  position:absolute;
  left:914px;
  top:684px;
  width:516px;
  height:36px;
  display:flex;
}
#u283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u284_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
}
#u284 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
  display:flex;
}
#u284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u284_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u285 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u286_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:32px;
}
#u286 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:11px;
  width:121px;
  height:32px;
  display:flex;
}
#u286 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u287 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:18px;
  width:19px;
  height:19px;
  display:flex;
}
#u287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u288_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u288 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:91px;
  width:24px;
  height:24px;
  display:flex;
}
#u288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u289_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u289 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:314px;
  width:24px;
  height:24px;
  display:flex;
}
#u289 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u290_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u290 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:313px;
  width:24px;
  height:24px;
  display:flex;
}
#u290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u291_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u291 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:90px;
  width:24px;
  height:24px;
  display:flex;
}
#u291 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u291_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u292 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:143px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u292 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u292_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u293 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:17px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u293 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u293_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u294 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u295 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:90px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u295 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u295_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u296_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u296 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u296 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u297 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:167px;
  width:129px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:16px;
  color:#1D1F20;
}
#u297 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u297_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u298 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:91px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u298 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u298_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u299 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:224px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u299 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u299_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u300 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:314px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u300 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u300_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u301 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:216px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u301 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u301_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u302_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u302 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:247px;
  width:208px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u302 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u302_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u303 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u303 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u303_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u304 {
  border-width:0px;
  position:absolute;
  left:466px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u304 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u304_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u305 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u305_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u306 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u306 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u306_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u307 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u307 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u307_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u308 {
  border-width:0px;
  position:absolute;
  left:1969px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u308 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u308_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u309 {
  border-width:0px;
  position:absolute;
  left:1779px;
  top:216px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u309 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u309_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u310 {
  border-width:0px;
  position:absolute;
  left:609px;
  top:247px;
  width:125px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u310 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u310_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u311 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:247px;
  width:82px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u311 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u311_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u312 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u313_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u313 {
  border-width:0px;
  position:absolute;
  left:908px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u313 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u313_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u314 {
  border-width:0px;
  position:absolute;
  left:734px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u314 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u314_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u315_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u315 {
  border-width:0px;
  position:absolute;
  left:1732px;
  top:247px;
  width:114px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u315 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u315_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u316_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:21px;
}
#u316 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:368px;
  width:24px;
  height:21px;
  display:flex;
}
#u316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u317_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u317 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:368px;
  width:24px;
  height:24px;
  display:flex;
}
#u317 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u317_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u318 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:368px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u318 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u318_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u319 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:265px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#414245;
}
#u319 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u319_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u320 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u321 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:90px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u321 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u321_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u322_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
}
#u322 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:84px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u322 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u322_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u323_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u323 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:94px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u323 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u323_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u324 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u325 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u326_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u326 {
  border-width:0px;
  position:absolute;
  left:1240px;
  top:84px;
  width:61px;
  height:32px;
  display:flex;
}
#u326 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u326_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u327 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u327 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u327_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u328 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u329_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u329 {
  border-width:0px;
  position:absolute;
  left:1313px;
  top:84px;
  width:52px;
  height:32px;
  display:flex;
}
#u329 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u330_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u330 {
  border-width:0px;
  position:absolute;
  left:1325px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u330 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u330_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u331_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u331 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:90px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u331 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u331_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u332_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u332 {
  border-width:0px;
  position:absolute;
  left:1406px;
  top:88px;
  width:24px;
  height:24px;
  display:flex;
}
#u332 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u332_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u333 {
  border-width:0px;
  position:absolute;
  left:1848px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u333 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u333_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u334 {
  border-width:0px;
  position:absolute;
  left:1827px;
  top:247px;
  width:100px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u334 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u334_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u335 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u335 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u335_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:40px;
}
#u336 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:247px;
  width:61px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  line-height:40px;
}
#u336 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u336_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u337 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u338 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u339_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
}
#u339 {
  border-width:0px;
  position:absolute;
  left:3346px;
  top:710px;
  width:61px;
  height:32px;
  display:flex;
}
#u339 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u339_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u340 {
  border-width:0px;
  position:absolute;
  left:3362px;
  top:716px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u340 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u340_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u341 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u342_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:32px;
}
#u342 {
  border-width:0px;
  position:absolute;
  left:3419px;
  top:710px;
  width:52px;
  height:32px;
  display:flex;
}
#u342 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u342_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u343_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u343 {
  border-width:0px;
  position:absolute;
  left:3431px;
  top:716px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#536FFE;
}
#u343 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u343_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u344 {
  border-width:0px;
  position:absolute;
  left:3483px;
  top:716px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1D1F20;
}
#u344 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u344_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u345_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u345 {
  border-width:0px;
  position:absolute;
  left:3512px;
  top:714px;
  width:24px;
  height:24px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u345 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u346 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1554px;
  height:946px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.6);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u347 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:1111px;
  width:1554px;
  height:946px;
  display:flex;
}
#u347 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u347_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1355px;
  height:694px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  -moz-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u348 {
  border-width:0px;
  position:absolute;
  left:186px;
  top:1219px;
  width:1355px;
  height:694px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u348 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u348_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1355px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u349 {
  border-width:0px;
  position:absolute;
  left:186px;
  top:1219px;
  width:1355px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u349 .text {
  position:absolute;
  align-self:center;
  padding:16px 24px 16px 24px;
  box-sizing:border-box;
  width:100%;
}
#u349_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u350 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u351_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u351 {
  border-width:0px;
  position:absolute;
  left:1504px;
  top:1240px;
  width:12px;
  height:12px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u351 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u352 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:1235px;
  width:56px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u352 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u352_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u353_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u353 {
  border-width:0px;
  position:absolute;
  left:410px;
  top:1235px;
  width:311px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u353 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u353_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1355px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-left:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:4px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u354 {
  border-width:0px;
  position:absolute;
  left:186px;
  top:1849px;
  width:1355px;
  height:64px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u354 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u354_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:32px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u355 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:1865px;
  width:57px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u355 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u355_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u356 {
  border-width:0px;
  position:absolute;
  left:969px;
  top:1881px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u356 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u356_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u357 {
  border-width:0px;
  position:absolute;
  left:969px;
  top:1929px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:3px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u358 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:1291px;
  width:393px;
  height:40px;
  display:flex;
}
#u358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u359_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u359 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:1331px;
  width:393px;
  height:40px;
  display:flex;
}
#u359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u360_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(231, 232, 233, 1);
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u360 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:1370px;
  width:393px;
  height:40px;
  display:flex;
}
#u360 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u360_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:center;
  line-height:40px;
}
#u361 {
  border-width:0px;
  position:absolute;
  left:375px;
  top:1424px;
  width:99px;
  height:40px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:center;
  line-height:40px;
}
#u361 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u361_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u362 {
  border-width:0px;
  position:absolute;
  left:503px;
  top:1302px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u362 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u362_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u363_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u363 {
  border-width:0px;
  position:absolute;
  left:403px;
  top:1302px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u363 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u363_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u364_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u364 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:1302px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u364 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u364_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u365 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u366 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:893px;
  height:529px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F0F7FF;
  text-align:left;
  line-height:22px;
}
#u367 {
  border-width:0px;
  position:absolute;
  left:617px;
  top:1291px;
  width:893px;
  height:529px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F0F7FF;
  text-align:left;
  line-height:22px;
}
#u367 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u368 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u369 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:1350px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u369 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u369_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u370_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u370 {
  border-width:0px;
  position:absolute;
  left:1204px;
  top:1350px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u370 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u370_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u371 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:1372px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u371 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u372 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u373_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u373 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1391px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u373 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u373_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u374_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u374 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:1391px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u374 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u374_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u375_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u375 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1413px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u375 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u376 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u377_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u377 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:1391px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u377 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u377_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u378_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u378 {
  border-width:0px;
  position:absolute;
  left:1205px;
  top:1391px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u378 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u379_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u379 {
  border-width:0px;
  position:absolute;
  left:1134px;
  top:1413px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u379 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u379_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u380 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u381 {
  border-width:0px;
  position:absolute;
  left:1096px;
  top:1431px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u381 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u381_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F3564B;
  line-height:22px;
}
#u382 {
  border-width:0px;
  position:absolute;
  left:1205px;
  top:1431px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F3564B;
  line-height:22px;
}
#u382 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u383 {
  border-width:0px;
  position:absolute;
  left:1134px;
  top:1453px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u383 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u383_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u384 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u385_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u385 {
  border-width:0px;
  position:absolute;
  left:665px;
  top:1431px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u385 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u385_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u386_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u386 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:1431px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u386 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u387_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u387 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:1453px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u387 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u387_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u388_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u388 {
  border-width:0px;
  position:absolute;
  left:642px;
  top:1302px;
  width:113px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u388 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u388_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u389_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:38px;
  background:inherit;
  background-color:rgba(240, 247, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u389 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:1333px;
  width:393px;
  height:38px;
  display:flex;
}
#u389 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u389_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u390_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:77px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:left;
  line-height:40px;
}
#u390 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:1333px;
  width:103px;
  height:77px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:left;
  line-height:40px;
}
#u390 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u390_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u391_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:77px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u391 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:1333px;
  width:171px;
  height:77px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u391 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u391_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u392_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u392 {
  border-width:0px;
  position:absolute;
  left:385px;
  top:1333px;
  width:79px;
  height:80px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u392 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u392_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u393 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u394_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u394 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1350px;
  width:71px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u394 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u394_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u395_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u395 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:1350px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u395 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u396 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1372px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u396 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u397 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u398 {
  border-width:0px;
  position:absolute;
  left:665px;
  top:1472px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u398 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u398_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F3564B;
  line-height:22px;
}
#u399 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:1472px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F3564B;
  line-height:22px;
}
#u399 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u400 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1494px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u401 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u402 {
  border-width:0px;
  position:absolute;
  left:1096px;
  top:1472px;
  width:99px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u402 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u402_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u403_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u403 {
  border-width:0px;
  position:absolute;
  left:1205px;
  top:1472px;
  width:200px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u403 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u404 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:1494px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u404 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u404_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u405 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u406 {
  border-width:0px;
  position:absolute;
  left:721px;
  top:1510px;
  width:43px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u406 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u406_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u407 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:1510px;
  width:200px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u407 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 32px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u408 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:1532px;
  width:354px;
  height:16px;
  display:flex;
  opacity:0;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:8px;
  color:#FFFFFF;
}
#u408 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u409 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u410 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:650px;
  height:79px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:22px;
}
#u411 {
  border-width:0px;
  position:absolute;
  left:769px;
  top:1728px;
  width:650px;
  height:79px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:22px;
}
#u411 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u411_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u412 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1728px;
  width:76px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u412 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u412_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u413 {
  border-width:0px;
  position:absolute;
  left:990px;
  top:1796px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u413 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u414 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u415 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:650px;
  height:145px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:22px;
}
#u416 {
  border-width:0px;
  position:absolute;
  left:769px;
  top:1555px;
  width:650px;
  height:145px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:22px;
}
#u416 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 12px;
  box-sizing:border-box;
  width:100%;
}
#u416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u417 {
  border-width:0px;
  position:absolute;
  left:665px;
  top:1556px;
  width:104px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u417 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u417_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u418 {
  border-width:0px;
  position:absolute;
  left:990px;
  top:1689px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u418 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:414px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u419 {
  border-width:0px;
  position:absolute;
  left:780px;
  top:1562px;
  width:414px;
  height:66px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u419 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u419_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u420 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:184px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u420 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u420_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u421 {
  border-width:0px;
  position:absolute;
  left:1178px;
  top:216px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u421 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u421_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:left;
  line-height:40px;
}
#u422 {
  border-width:0px;
  position:absolute;
  left:1165px;
  top:247px;
  width:187px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:left;
  line-height:40px;
}
#u422 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u422_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u423 {
  border-width:0px;
  position:absolute;
  left:1375px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u423 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u423_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:left;
  line-height:40px;
}
#u424 {
  border-width:0px;
  position:absolute;
  left:1356px;
  top:247px;
  width:117px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  text-align:left;
  line-height:40px;
}
#u424 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u424_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u425 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u426 {
  border-width:0px;
  position:absolute;
  left:1971px;
  top:247px;
  width:57px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u426 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 12px;
  box-sizing:border-box;
  width:100%;
}
#u426_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u427 {
  border-width:0px;
  position:absolute;
  left:1938px;
  top:247px;
  width:33px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#536FFE;
  text-align:left;
  line-height:40px;
}
#u427 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
  width:100%;
}
#u427_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u428_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u428 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:247px;
  width:171px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u428 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u428_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u429 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u430 {
  border-width:0px;
  position:absolute;
  left:1543px;
  top:216px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u430 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u430_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u431_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:10px;
}
#u431 {
  border-width:0px;
  position:absolute;
  left:1603px;
  top:221px;
  width:7px;
  height:10px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  line-height:18px;
}
#u431 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u431_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u432 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u433_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u433 {
  border-width:0px;
  position:absolute;
  left:1680px;
  top:216px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  color:#1D1F20;
}
#u433 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u433_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u434_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:10px;
}
#u434 {
  border-width:0px;
  position:absolute;
  left:1726px;
  top:221px;
  width:7px;
  height:10px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  line-height:18px;
}
#u434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:399px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u435 {
  border-width:0px;
  position:absolute;
  left:1662px;
  top:247px;
  width:79px;
  height:399px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1D1F20;
  line-height:40px;
}
#u435 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u435_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u436_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:54px;
}
#u436 {
  border-width:0px;
  position:absolute;
  left:2088px;
  top:755px;
  width:187px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF3399;
  text-align:left;
  line-height:18px;
}
#u436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u437 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u438 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u439 {
  border-width:0px;
  position:absolute;
  left:3263px;
  top:517px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u439 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u440_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u440 {
  border-width:0px;
  position:absolute;
  left:3502px;
  top:527px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u440 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u441_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u441 {
  border-width:0px;
  position:absolute;
  left:3263px;
  top:549px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u441 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u442 {
  border-width:0px;
  position:absolute;
  left:3164px;
  top:517px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u442 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u442_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u443 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u444 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u445 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:710px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u445 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u445_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u446 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:742px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u447 {
  border-width:0px;
  position:absolute;
  left:2370px;
  top:710px;
  width:56px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u447 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u447_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u448 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u449 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u450 {
  border-width:0px;
  position:absolute;
  left:3263px;
  top:582px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  line-height:22px;
}
#u450 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u451_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u451 {
  border-width:0px;
  position:absolute;
  left:3263px;
  top:614px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u452 {
  border-width:0px;
  position:absolute;
  left:3192px;
  top:582px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u452 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u452_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u453 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u454 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u455 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:582px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u455 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u456_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u456 {
  border-width:0px;
  position:absolute;
  left:2665px;
  top:592px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u456 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u457 {
  border-width:0px;
  position:absolute;
  left:2426px;
  top:614px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u457 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u457_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u458 {
  border-width:0px;
  position:absolute;
  left:2327px;
  top:582px;
  width:99px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u458 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u458_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u459 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u460 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u461 {
  border-width:0px;
  position:absolute;
  left:2831px;
  top:582px;
  width:264px;
  height:32px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u461 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u462_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u462 {
  border-width:0px;
  position:absolute;
  left:3070px;
  top:592px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u462 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:264px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u463 {
  border-width:0px;
  position:absolute;
  left:2831px;
  top:614px;
  width:264px;
  height:24px;
  display:flex;
  opacity:0;
  color:#FFFFFF;
}
#u463 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u464 {
  border-width:0px;
  position:absolute;
  left:2760px;
  top:582px;
  width:71px;
  height:56px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u464 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 29px 0px;
  box-sizing:border-box;
  width:100%;
}
#u464_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u465 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:946px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.6);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u466 {
  border-width:0px;
  position:absolute;
  left:1794px;
  top:1111px;
  width:1024px;
  height:946px;
  display:flex;
}
#u466 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u467 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u468_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:651px;
  height:726px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  -moz-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  box-shadow:0px 1px 4px rgba(0, 0, 0, 0.2);
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u468 {
  border-width:0px;
  position:absolute;
  left:1981px;
  top:1221px;
  width:651px;
  height:726px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  line-height:18px;
}
#u468 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u469_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:651px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 235, 235, 1);
  border-radius:4px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u469 {
  border-width:0px;
  position:absolute;
  left:1981px;
  top:1221px;
  width:651px;
  height:54px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u469 .text {
  position:absolute;
  align-self:center;
  padding:16px 24px 16px 24px;
  box-sizing:border-box;
  width:100%;
}
#u469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u470 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u471_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u471 {
  border-width:0px;
  position:absolute;
  left:2606px;
  top:1242px;
  width:12px;
  height:12px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u471 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:651px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-left:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:4px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u472 {
  border-width:0px;
  position:absolute;
  left:1981px;
  top:1883px;
  width:651px;
  height:64px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:rgba(0, 0, 0, 0.427450980392157);
  text-align:left;
  line-height:22px;
}
#u472 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u473 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:32px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u474 {
  border-width:0px;
  position:absolute;
  left:2540px;
  top:1899px;
  width:66px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u474 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u474_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u475_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:21px;
}
#u475 {
  border-width:0px;
  position:absolute;
  left:2470px;
  top:1899px;
  width:66px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:21px;
}
#u475 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u475_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u476 {
  border-width:0px;
  position:absolute;
  left:2548px;
  top:1883px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u477 {
  border-width:0px;
  position:absolute;
  left:2548px;
  top:1931px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u477 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u477_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u478 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u479 {
  border-width:0px;
  position:absolute;
  left:2080px;
  top:1311px;
  width:56px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u479 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u479_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u480 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1311px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u480 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u481_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u481 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1343px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u481 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u482 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u483_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u483 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1423px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u483 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u484_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u484 {
  border-width:0px;
  position:absolute;
  left:2065px;
  top:1423px;
  width:71px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u484 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u484_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u485 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1455px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u485 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u486 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u487_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u487 {
  border-width:0px;
  position:absolute;
  left:2554px;
  top:1433px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u487 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u487_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u488_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u488 {
  border-width:0px;
  position:absolute;
  left:2567px;
  top:1435px;
  width:8px;
  height:8px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u489 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u490_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u490 {
  border-width:0px;
  position:absolute;
  left:2065px;
  top:1367px;
  width:71px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u490 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u490_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u491_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
}
#u491 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1367px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u491 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u492_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u492 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1399px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u492 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u493 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u494 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1535px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u494 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u495_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u495 {
  border-width:0px;
  position:absolute;
  left:2065px;
  top:1535px;
  width:71px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u495 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u495_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u496_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u496 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1567px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u496 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u497 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u498_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u498 {
  border-width:0px;
  position:absolute;
  left:2554px;
  top:1545px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u498 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u499_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u499 {
  border-width:0px;
  position:absolute;
  left:2567px;
  top:1547px;
  width:8px;
  height:8px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u499 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u499_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u500 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u501 {
  border-width:0px;
  position:absolute;
  left:2065px;
  top:1479px;
  width:71px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u501 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u501_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u502_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
}
#u502 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1479px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u502 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u502_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u503 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1511px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u503 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u504 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u505 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u506_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u506 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1591px;
  width:440px;
  height:32px;
  display:flex;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u506 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u507_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u507 {
  border-width:0px;
  position:absolute;
  left:2037px;
  top:1591px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u507 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u507_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u508_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u508 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1623px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u508 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u508_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u509_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
}
#u509 {
  border-width:0px;
  position:absolute;
  left:2555px;
  top:1601px;
  width:14px;
  height:16px;
  display:flex;
  font-family:'anticon', 'anticon Medium', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:28px;
  color:rgba(0, 0, 0, 0.427450980392157);
}
#u509 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u510 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u511 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u512_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u512 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1647px;
  width:440px;
  height:32px;
  display:flex;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u512 .text {
  position:absolute;
  align-self:center;
  padding:2px 8px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u512_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u513 {
  border-width:0px;
  position:absolute;
  left:2037px;
  top:1647px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u513 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u513_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u514_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u514 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1679px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u514 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u514_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u515_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
}
#u515 {
  border-width:0px;
  position:absolute;
  left:2555px;
  top:1657px;
  width:14px;
  height:16px;
  display:flex;
  font-family:'anticon', 'anticon Medium', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:28px;
  color:rgba(0, 0, 0, 0.427450980392157);
}
#u515 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u515_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u516 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u517 {
  border-width:0px;
  position:absolute;
  left:2093px;
  top:1703px;
  width:43px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u517 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u517_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u518_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
}
#u518 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1703px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u518 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u518_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u519_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u519 {
  border-width:0px;
  position:absolute;
  left:2136px;
  top:1735px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u519 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u519_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
