﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:2045px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1115px;
  height:146px;
}
#u827 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:900px;
  width:1115px;
  height:146px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
  text-align:left;
}
#u827 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u828 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u829 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1073px;
  height:2px;
}
#u830 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1121px;
  width:1072px;
  height:1px;
  display:flex;
}
#u830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u831 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1098px;
  width:24px;
  height:24px;
}
#u831_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u831_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u832 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1122px;
  width:24px;
  height:24px;
}
#u832_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u832_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u833 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:1202px;
  width:211px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u833 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u833_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u834 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u835 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1202px;
  width:123px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u835 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u836 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1234px;
  width:123px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u836 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u837 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:1146px;
  width:129px;
  height:24px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u837 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u837_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u838_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1073px;
  height:2px;
}
#u838 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1511px;
  width:1072px;
  height:1px;
  display:flex;
}
#u838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u839 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1488px;
  width:24px;
  height:24px;
}
#u839_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u839_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u840 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1512px;
  width:24px;
  height:24px;
}
#u840_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u840_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u842 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:1592px;
  width:113px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u842 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u842_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u843_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u843 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1592px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u843 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u844 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1624px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u844 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u845 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:1536px;
  width:129px;
  height:24px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u845 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u845_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u846 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u847 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:1202px;
  width:103px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u847 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u848 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:1234px;
  width:103px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u848 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u849 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u850 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:1212px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u850 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u851 {
  border-width:0px;
  position:absolute;
  left:451px;
  top:1211px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u852 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:1229px;
  width:15px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u852 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u852_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u853 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u854 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u855 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u856 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:1705px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u856 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u856_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u857 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1712px;
  width:14px;
  height:14px;
  display:flex;
}
#u857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u858 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:1704px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u858 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u858_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u859 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u860 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u861 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:1705px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u861 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u861_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u862 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:1712px;
  width:14px;
  height:14px;
  display:flex;
}
#u862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u863_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
}
#u863 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:1714px;
  width:10px;
  height:10px;
  display:flex;
}
#u863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u864 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u865 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u866 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1736px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u866 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u867 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u869 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u870 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:1761px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u870 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u870_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u871 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1768px;
  width:14px;
  height:14px;
  display:flex;
}
#u871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u872 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:1760px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u872 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u872_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u873 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u874 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u875 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:1761px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u875 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u875_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u876 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:1768px;
  width:14px;
  height:14px;
  display:flex;
}
#u876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
}
#u877 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:1770px;
  width:10px;
  height:10px;
  display:flex;
}
#u877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u878 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u879 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u880 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1792px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u880 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u881 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u882 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u883_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u883 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:1816px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u883 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u883_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u884 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u885 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1816px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u885 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u885_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u886 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u887_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u887 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1825px;
  width:14px;
  height:14px;
  display:flex;
}
#u887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:6px;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:1829px;
  width:8px;
  height:6px;
  display:flex;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u889_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1848px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u889 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u891_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:336px;
  top:1816px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u891 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u893 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:1825px;
  width:14px;
  height:14px;
  display:flex;
}
#u893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:6px;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:1829px;
  width:8px;
  height:6px;
  display:flex;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u895 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1320px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u896 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u897 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:1320px;
  width:141px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u897 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u897_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u898 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1352px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u898 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u899 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u900 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:1330px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u900 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u901 {
  border-width:0px;
  position:absolute;
  left:660px;
  top:1329px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u902 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1329px;
  width:64px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u902 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u902_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u903 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:1205px;
  width:64px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u903 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u903_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F5222D;
  line-height:22px;
}
#u904 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1234px;
  width:43px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F5222D;
  line-height:22px;
}
#u904 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u904_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u905 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:1290px;
  width:15px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u905 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u905_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u906 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u907_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u907 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1264px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u907 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u908 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:1264px;
  width:141px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u908 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u908_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u909 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1296px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u909 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u910 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u911 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:1274px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u911 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u912 {
  border-width:0px;
  position:absolute;
  left:660px;
  top:1273px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u913 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1270px;
  width:64px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u913 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u913_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u914 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u915 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u916 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:1648px;
  width:113px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u916 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u916_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u917 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1648px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u917 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u918_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u918 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:1680px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u918 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u919 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u920 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:1592px;
  width:103px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u920 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u921_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u921 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:1624px;
  width:103px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u921 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u922 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u923 {
  border-width:0px;
  position:absolute;
  left:780px;
  top:1602px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u923 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u924 {
  border-width:0px;
  position:absolute;
  left:794px;
  top:1601px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u925 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:1595px;
  width:64px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u925 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u925_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u926 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u927 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:1648px;
  width:103px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u927 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:1680px;
  width:103px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u928 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:780px;
  top:1658px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u930 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:794px;
  top:1657px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:1651px;
  width:64px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FF3399;
}
#u932 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u932_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:316px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:790px;
  top:1081px;
  width:194px;
  height:316px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u933 .text {
  position:absolute;
  align-self:center;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1073px;
  height:2px;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:1214px;
  width:1072px;
  height:1px;
  display:flex;
}
#u934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:1373px;
  top:1144px;
  width:24px;
  height:24px;
}
#u935_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u935_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:1373px;
  top:1168px;
  width:24px;
  height:24px;
}
#u936_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u936_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:1394px;
  top:1248px;
  width:211px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u937 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u937_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u939 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1248px;
  width:123px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u939 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u940 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1280px;
  width:123px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u940 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u941 {
  border-width:0px;
  position:absolute;
  left:1408px;
  top:1192px;
  width:129px;
  height:24px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u941 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u941_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1073px;
  height:2px;
}
#u942 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:1529px;
  width:1072px;
  height:1px;
  display:flex;
}
#u942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u943 {
  border-width:0px;
  position:absolute;
  left:1373px;
  top:1459px;
  width:24px;
  height:24px;
}
#u943_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u943_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u944 {
  border-width:0px;
  position:absolute;
  left:1373px;
  top:1483px;
  width:24px;
  height:24px;
}
#u944_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u944_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u945 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u946 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:1563px;
  width:113px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u946 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u946_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u947 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1563px;
  width:124px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u947 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u948 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1595px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u948 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u949 {
  border-width:0px;
  position:absolute;
  left:1408px;
  top:1507px;
  width:129px;
  height:24px;
  display:flex;
  font-family:'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:16px;
  color:rgba(0, 0, 0, 0.847058823529412);
  line-height:24px;
}
#u949 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u949_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u950 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u951 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:1248px;
  width:103px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u951 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u952 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:1280px;
  width:103px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u952 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u953 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u954 {
  border-width:0px;
  position:absolute;
  left:1810px;
  top:1258px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u954 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u955_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u955 {
  border-width:0px;
  position:absolute;
  left:1824px;
  top:1257px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u956 {
  border-width:0px;
  position:absolute;
  left:1442px;
  top:1275px;
  width:15px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u956 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u956_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:1618px;
  top:1676px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u960 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u960_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1683px;
  width:14px;
  height:14px;
  display:flex;
}
#u961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1675px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u962 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u962_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u965_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u965 {
  border-width:0px;
  position:absolute;
  left:1707px;
  top:1676px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u965 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u965_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:1693px;
  top:1683px;
  width:14px;
  height:14px;
  display:flex;
}
#u966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:1695px;
  top:1685px;
  width:10px;
  height:10px;
  display:flex;
}
#u967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u969 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u970_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u970 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1707px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u970 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u971 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u972 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u973 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u974 {
  border-width:0px;
  position:absolute;
  left:1618px;
  top:1732px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u974 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u974_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u975 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1739px;
  width:14px;
  height:14px;
  display:flex;
}
#u975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u976_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u976 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1731px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u976 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u976_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u977 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u978 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u979 {
  border-width:0px;
  position:absolute;
  left:1707px;
  top:1732px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u979 .text {
  position:absolute;
  align-self:center;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u979_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u980 {
  border-width:0px;
  position:absolute;
  left:1693px;
  top:1739px;
  width:14px;
  height:14px;
  display:flex;
}
#u980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
}
#u981 {
  border-width:0px;
  position:absolute;
  left:1695px;
  top:1741px;
  width:10px;
  height:10px;
  display:flex;
}
#u981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u982 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u983 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u984 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1763px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u984 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u985 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u986 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u987 {
  border-width:0px;
  position:absolute;
  left:1506px;
  top:1787px;
  width:99px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u987 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u987_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u988 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u989 {
  border-width:0px;
  position:absolute;
  left:1619px;
  top:1787px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u989 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u989_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u990 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u991 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1796px;
  width:14px;
  height:14px;
  display:flex;
}
#u991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u992_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:6px;
}
#u992 {
  border-width:0px;
  position:absolute;
  left:1608px;
  top:1800px;
  width:8px;
  height:6px;
  display:flex;
}
#u992 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u993 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1819px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u993 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u994 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u995 {
  border-width:0px;
  position:absolute;
  left:1709px;
  top:1787px;
  width:61px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.647058823529412);
  line-height:22px;
}
#u995 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 24px 5px 8px;
  box-sizing:border-box;
  width:100%;
}
#u995_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u996 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u997 {
  border-width:0px;
  position:absolute;
  left:1695px;
  top:1796px;
  width:14px;
  height:14px;
  display:flex;
}
#u997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u998_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:6px;
}
#u998 {
  border-width:0px;
  position:absolute;
  left:1698px;
  top:1800px;
  width:8px;
  height:6px;
  display:flex;
}
#u998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u999 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1000 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1366px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1000 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u1000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1001 {
  border-width:0px;
  position:absolute;
  left:1464px;
  top:1366px;
  width:141px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1001 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1001_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1002 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1398px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1002 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1003 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u1004 {
  border-width:0px;
  position:absolute;
  left:2019px;
  top:1376px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1004 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u1005 {
  border-width:0px;
  position:absolute;
  left:2033px;
  top:1375px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u1005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1005_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F5222D;
  line-height:22px;
}
#u1006 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1280px;
  width:43px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F5222D;
  line-height:22px;
}
#u1006 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1006_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1007 {
  border-width:0px;
  position:absolute;
  left:1442px;
  top:1336px;
  width:15px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1007 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1007_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1008 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1009 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1310px;
  width:440px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1009 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u1009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1010 {
  border-width:0px;
  position:absolute;
  left:1464px;
  top:1310px;
  width:141px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1010 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1010_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1011 {
  border-width:0px;
  position:absolute;
  left:1605px;
  top:1342px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1011 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1011_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1012 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1013_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u1013 {
  border-width:0px;
  position:absolute;
  left:2019px;
  top:1320px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1013 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1013_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u1014 {
  border-width:0px;
  position:absolute;
  left:2033px;
  top:1319px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u1014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1015 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1016 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:1619px;
  width:113px;
  height:35px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1016 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1016_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u1017 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1619px;
  width:124px;
  height:32px;
  display:flex;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.247058823529412);
  text-align:left;
  line-height:22px;
}
#u1017 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 5px 12px;
  box-sizing:border-box;
  width:100%;
}
#u1017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1018_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:440px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1018 {
  border-width:0px;
  position:absolute;
  left:1604px;
  top:1651px;
  width:440px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1018 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1019 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1020_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1020 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:1563px;
  width:103px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1020 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u1020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1021 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:1595px;
  width:103px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1021 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1022 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1023_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u1023 {
  border-width:0px;
  position:absolute;
  left:1810px;
  top:1573px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1023 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1023_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u1024 {
  border-width:0px;
  position:absolute;
  left:1824px;
  top:1572px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u1024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1025 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1026 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:1619px;
  width:103px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#000000;
  text-align:left;
  line-height:22px;
}
#u1026 .text {
  position:absolute;
  align-self:center;
  padding:2px 12px 2px 12px;
  box-sizing:border-box;
  width:100%;
}
#u1026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1027 {
  border-width:0px;
  position:absolute;
  left:1733px;
  top:1651px;
  width:103px;
  height:24px;
  display:flex;
  font-family:'MicrosoftTaiLe', 'Microsoft Tai Le', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.427450980392157);
  line-height:22px;
}
#u1027 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1028 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1029_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u1029 {
  border-width:0px;
  position:absolute;
  left:1810px;
  top:1629px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  opacity:0.449999988079071;
  font-family:'Microsoft YaHei', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1029 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1030_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 51, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
  color:#FFFFFF;
}
#u1030 {
  border-width:0px;
  position:absolute;
  left:1824px;
  top:1628px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
  font-size:8px;
  color:#FFFFFF;
}
#u1030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1031 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1032_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:813px;
}
#u1032 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:1460px;
  height:813px;
  display:flex;
}
#u1032 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1033_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:43px;
}
#u1033 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:183px;
  width:172px;
  height:43px;
  display:flex;
}
#u1033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1034_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
}
#u1034 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1460px;
  height:54px;
  display:flex;
}
#u1034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1035 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1036_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:32px;
}
#u1036 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:11px;
  width:121px;
  height:32px;
  display:flex;
}
#u1036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1037_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u1037 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:18px;
  width:19px;
  height:19px;
  display:flex;
}
#u1037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1038 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:91px;
  width:24px;
  height:24px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u1038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1039_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1039 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:149px;
  width:24px;
  height:24px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u1039 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1039_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1040_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1040 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:148px;
  width:24px;
  height:24px;
  display:flex;
}
#u1040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1041_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1041 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:90px;
  width:24px;
  height:24px;
  display:flex;
}
#u1041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u1042 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:17px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#1064FF;
}
#u1042 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1042_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u1043 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:91px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u1043 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1043_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u1044 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:149px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u1044 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1044_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1045_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:21px;
}
#u1045 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:257px;
  width:24px;
  height:21px;
  display:flex;
}
#u1045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1046_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1046 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:257px;
  width:24px;
  height:24px;
  display:flex;
}
#u1046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1047_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u1047 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:257px;
  width:97px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#414245;
}
#u1047 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1047_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u1048 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:194px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u1048 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1048_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1049_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1247px;
  height:236px;
}
#u1049 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:69px;
  width:1247px;
  height:236px;
  display:flex;
}
#u1049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
