﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,_(L,M,N,M),O,P,Q,null,R,S,T,U,V,W,X,S,Y,Z,ba,_(F,G,H,bb),bc,S,bd,Z,be,_(bf,bg,bh,bi,bj,bi,bk,bi,bl,k,H,_(bm,bn,bo,bn,bp,bn,bq,br)),i,_(j,k,l,k)),bs,_(),bt,_(),bu,_(bv,[_(bw,bx,by,h,bz,bA,u,bB,bC,bB,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),bQ,_(bR,bS,bT,bU)),bs,_(),bV,_(),bW,[_(bw,bX,by,h,bz,bY,u,bZ,bC,bZ,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,ca,i,_(j,cb,l,cc),J,null),bs,_(),bV,_(),cd,_(ce,cf)),_(bw,cg,by,h,bz,bY,u,bZ,bC,bZ,bD,bE,z,_(V,bF,bG,bH,bI,bJ,bK,bL,bM,_(F,G,H,bN,bO,bP),A,ca,i,_(j,cb,l,ch),J,null,bQ,_(bR,k,bT,ci)),bs,_(),bV,_(),cd,_(ce,cj))],ck,bg)])),cl,_(),cm,_(cn,_(co,cp),cq,_(co,cr),cs,_(co,ct)));}; 
var b="url",c="物联平台.html",d="generationDate",e=new Date(1748279432323.71),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="09ba522086054234bcf629d1cecd75a2",u="type",v="Axure:Page",w="物联平台",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="horizontal",M="near",N="vertical",O="imageRepeat",P="auto",Q="favicon",R="sketchFactor",S="0",T="colorStyle",U="appliedColor",V="fontName",W="Applied Font",X="borderWidth",Y="borderVisibility",Z="all",ba="borderFill",bb=0xFF797979,bc="cornerRadius",bd="cornerVisibility",be="outerShadow",bf="on",bg=false,bh="offsetX",bi=5,bj="offsetY",bk="blurRadius",bl="spread",bm="r",bn=0,bo="g",bp="b",bq="a",br=0.349019607843137,bs="adaptiveStyles",bt="interactionMap",bu="diagram",bv="objects",bw="id",bx="e5eb988693294965a93bd5c23b6b39e6",by="label",bz="friendlyType",bA="Group",bB="layer",bC="styleType",bD="visible",bE=true,bF="'ArialMT', 'Arial', sans-serif",bG="fontWeight",bH="400",bI="fontStyle",bJ="normal",bK="fontStretch",bL="5",bM="foreGroundFill",bN=0xFF333333,bO="opacity",bP=1,bQ="location",bR="x",bS=12,bT="y",bU=10,bV="imageOverrides",bW="objs",bX="dab3d716f2ac4cf081eb4331ef69a596",bY="Image",bZ="imageBox",ca="********************************",cb=1440,cc=46,cd="images",ce="normal~",cf="images/物联平台/u1058.png",cg="92a98d05210a413790659f126e8c65f3",ch=658,ci=44,cj="images/物联平台/u1059.png",ck="propagate",cl="masters",cm="objectPaths",cn="e5eb988693294965a93bd5c23b6b39e6",co="scriptId",cp="u1057",cq="dab3d716f2ac4cf081eb4331ef69a596",cr="u1058",cs="92a98d05210a413790659f126e8c65f3",ct="u1059";
return _creator();
})());